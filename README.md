# ScrapeAI - Music Charts Pipeline

A Python-based ETL pipeline that scrapes music chart data from musicchartsarchive.com and stores it as JSON in a PostgreSQL database. The pipeline extracts singles and albums chart data with configurable date ranges and provides robust error handling and logging.

## Features

- **Web Scraping**: Scrapes music chart data from musicchartsarchive.com
- **JSON Storage**: Stores complete chart data as JSON in PostgreSQL database
- **Chart Types**: Handles both singles and albums charts
- **Configurable**: Environment-specific configurations for development and production
- **Robust Logging**: Comprehensive logging with optional Slack notifications
- **Error Handling**: Retry mechanisms and graceful error handling
- **Docker Support**: Containerized deployment ready

## Project Structure

```
scrape_pipeline/
├── config/                 # Configuration files
│   ├── config.yaml        # Production configuration
│   ├── config_dev.yaml    # Development configuration
│   └── config_loader.py   # Configuration loader utility
├── libs/                  # Core libraries
│   ├── clients/           # External API clients
│   │   └── music_charts_client.py
│   ├── database/          # Database abstractions
│   │   ├── database.py    # Abstract database interface
│   │   ├── supabase.py    # Main Supabase database class
│   │   ├── supabase_raw.py # Raw data operations
│   │   └── supabase_log.py # Logging operations
│   └── logging/           # Logging utilities
│       ├── slack.py       # Slack integration
│       └── slack_log_handler.py
├── raw/                   # ETL job implementations
│   ├── common.py          # Data validation utilities
│   └── music_charts_etl.py # Main ETL job
├── docs/                  # Documentation
│   └── code-architecture.md
├── data/                  # Sample data (development)
├── main.py               # Application entry point
├── test_database.py      # Database connection test
├── requirements.txt      # Python dependencies
├── Dockerfile           # Docker configuration
├── .env.example         # Environment variables template
└── .venv/               # Virtual environment (local)
```

## Quick Start

1. **Activate virtual environment**: `source .venv/bin/activate`
2. **Set up environment**: `cp .env.example .env` and edit with your database credentials
3. **Test database**: `python test_database.py`
4. **Run pipeline**: `python main.py -dev`

## Prerequisites

- Python 3.11+
- Virtual environment (`.venv` included in project)
- Supabase PostgreSQL database
- Slack webhook URL (optional, for error notifications)

## Installation

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scrape_pipeline
   ```

2. **Activate the virtual environment**
   ```bash
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies (if needed)**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase database credentials
   ```

### Docker Deployment

1. **Build the Docker image**
   ```bash
   docker build -t scrape-ai .
   ```

2. **Run with environment variables**
   ```bash
   docker run -e SUPABASE_URL=your_url -e SUPABASE_KEY=your_key scrape-ai
   ```

## Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Supabase Configuration (Required)
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key

# Slack Notifications (Optional)
SLACK_WEBHOOK_URL=
```

### Configuration Files

- **Production**: `config/config.yaml`
- **Development**: `config/config_dev.yaml`

Key configuration options:
- `chart_types`: Types of charts to scrape (singles, albums)
- `date_range`: Start and end years for data collection
- `request_delay`: Delay between requests (be respectful to the source)
- `retry_limit`: Number of retry attempts for failed requests

## Usage

### Development Mode

```bash
source .venv/bin/activate
python main.py -dev
```

This will:
- Load development configuration
- Use smaller batch sizes
- Load environment variables from `.env` file
- Process limited data for testing

### Production Mode

```bash
source .venv/bin/activate
python main.py
```

This will:
- Load production configuration
- Use optimized batch sizes
- Process full date ranges
- Send error notifications to Slack (if configured)

### Docker Usage

```bash
# Development
docker run -v $(pwd)/.env:/app/.env scrape-ai python main.py -dev

# Production
docker run -e SUPABASE_URL=your_url -e SUPABASE_KEY=your_key scrape-ai
```

## Database Storage

### PostgreSQL with JSON Storage

The pipeline stores chart data as JSON in a PostgreSQL database via Supabase API:
- **Table Name**: `music_archive`
- **Table Schema**:
  - `id` (uuid) - Primary key
  - `chart_date` (date) - Chart date (e.g., '2024-01-01')
  - `chart_type` (text) - Either 'single_chart' or 'album_chart'
  - `chart_data` (jsonb) - Complete chart data as JSON
  - `created_at` (timestamptz) - Timestamp with timezone
- **Access Method**: Supabase API client (no direct PostgreSQL connection needed)
- **JSON Format**: Complete chart data stored as JSONB for efficient querying
- **Automatic table creation** with proper indexes
- **Upsert logic** to handle data updates
- **Scalable cloud storage** via Supabase

### Example Data Structure

Each row contains complete chart data for a specific date and type:

```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "chart_date": "2024-01-01",
  "chart_type": "single_chart",
  "chart_data": [
    {
      "single_title": "Song Title",
      "artist": "Artist Name",
      "chart_date": "2024-01-01",
      "position": 1,
      "url": "https://example.com/song"
    }
  ],
  "created_at": "2024-01-01T12:00:00Z"
}
```

## Testing

### Database Connection Test

Test your database connection and table creation:

```bash
source .venv/bin/activate
python test_database.py
```

This will:
- Test database connectivity
- Create the `music_archive` table if it doesn't exist
- Insert sample data as JSON
- Verify data retrieval functionality

### Running the Pipeline

1. **Start with development mode** to test with limited data:
   ```bash
   source .venv/bin/activate
   python main.py -dev
   ```

2. **Check logs** for any errors or warnings

3. **Verify data** in your PostgreSQL database

## Monitoring and Logging

- **Console Logging**: All runs log to console with timestamps
- **Slack Notifications**: Error-level logs sent to Slack (production only)
- **Progress Tracking**: Detailed progress information during scraping
- **Statistics**: Summary statistics after each run

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify database credentials are correct
   - Check network connectivity to Supabase
   - Ensure database exists and is accessible

2. **Rate Limiting**
   - Increase `request_delay` in configuration
   - Reduce batch sizes for development

3. **Missing Data**
   - Check date ranges in configuration
   - Verify source website availability
   - Review logs for specific error messages

### Debug Mode

Run with increased logging:
```bash
source .venv/bin/activate
python -u main.py -dev  # -u for unbuffered output
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly in development mode
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
- Check the logs for detailed error messages
- Review configuration files
- Test with development mode first
- Check database for data verification

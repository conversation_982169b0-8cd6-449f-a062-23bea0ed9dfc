# ScrapeAI Music Charts Pipeline - Code Architecture & Flow

This document provides a detailed explanation of how the Music Charts Pipeline works, including function-to-function call flows, class interactions, and the complete data processing pipeline.

## Table of Contents

1. [High-Level Architecture](#high-level-architecture)
2. [Application Entry Point](#application-entry-point)
3. [Configuration System](#configuration-system)
4. [Logging System](#logging-system)
5. [Web Scraping Client](#web-scraping-client)
6. [ETL Pipeline](#etl-pipeline)
7. [Storage Systems](#storage-systems)
8. [Data Validation](#data-validation)
9. [Complete Function Call Flow](#complete-function-call-flow)

## High-Level Architecture

The pipeline follows a modular ETL (Extract, Transform, Load) architecture:

```
main.py
├── Configuration Loading (config_loader.py)
├── Logging Setup (slack.py, slack_log_handler.py)
├── Client Initialization (music_charts_client.py)
└── ETL Execution (musiccharts_etl.py)
    ├── Database Connection (supabase.py)
    ├── Data Extraction (music_charts_client.py)
    ├── Data Validation (common.py)
    └── Data Loading (database classes)
```

## Application Entry Point

### main.py - Application Bootstrap

The application starts in `main.py` with the following execution flow:

#### 1. Environment Detection
```python
is_dev = len(sys.argv) > 1 and sys.argv[1] == '-dev'
if is_dev:
    from dotenv import load_dotenv
    load_dotenv()
```
**Purpose**: Determines if running in development mode and loads `.env` file accordingly.

#### 2. Environment Variables Loading
```python
SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "")
SLACK_WEBHOOK_URL = os.getenv("SLACK_WEBHOOK_URL", "")
```
**Purpose**: Loads required environment variables for external services.

#### 3. Configuration Loading
```python
try:
    if is_dev:
        config = load_config("dev")
    else:
        config = load_config("prod")
except Exception as e:
    sys.exit(1)
```
**Function Called**: `load_config()` from `config/config_loader.py`
**Purpose**: Loads environment-specific configuration (dev vs prod).

#### 4. Logger Setup
```python
logger = Logger("music_charts_pipeline")
logger.setLevel(INFO)

console_handler = StreamHandler()
console_handler.setLevel(INFO)
console_formatter = Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)
```
**Purpose**: Creates a structured logger with console output.

#### 5. Slack Integration (Production Only)
```python
if not is_dev and SLACK_WEBHOOK_URL:
    try:
        slack = Slack(SLACK_WEBHOOK_URL)
        slack_handler = SlackLogHandler(slack)
        slack_handler.setLevel(ERROR)
        logger.addHandler(slack_handler)
    except Exception as e:
        pass
```
**Classes Instantiated**:
- `Slack` from `libs/logging/slack.py`
- `SlackLogHandler` from `libs/logging/slack_log_handler.py`
**Purpose**: Adds Slack error notifications for production runs.

#### 6. Client Initialization
```python
music_charts_client = MusicChartsClient(
    base_url=raw_music_charts_config.get("base_url"),
    retry_limit=raw_music_charts_config.get("retry_limit", 3),
    retry_delay=raw_music_charts_config.get("retry_delay", 2),
    logger=logger
)
```
**Class Instantiated**: `MusicChartsClient` from `libs/clients/music_charts_client.py`
**Purpose**: Creates the web scraping client with retry logic.

#### 7. Main Execution
```python
def main():
    logger.info("Music Charts Pipeline started.")
    try:
        raw_musiccharts_job(music_charts_client, raw_music_charts_config, logger)
        logger.info("Music Charts Pipeline completed successfully.")
    except KeyboardInterrupt:
        logger.warning("Pipeline interrupted by user.")
    except Exception as e:
        logger.error(f"Pipeline failed with error: {e}", exc_info=True)
        sys.exit(1)
```
**Function Called**: `raw_musiccharts_job()` from `raw/musiccharts_etl.py`
**Purpose**: Executes the main ETL pipeline with error handling.

## Configuration System

### config_loader.py - Configuration Management

#### load_config(env: str) Function
```python
def load_config(env: str = "prod") -> dict | None:
    file_name = __get_filename(env)
    try:
        with open(file_name, "r", encoding="utf-8") as stream:
            return yaml.safe_load(stream)
    except FileNotFoundError:
        raise RuntimeError(f"Config file {file_name} not found")
    except PermissionError:
        raise RuntimeError(f"Permission denied to read config file {file_name}")
    except OSError as e:
        raise RuntimeError(f"Error reading config file {file_name}: {e}")
    except yaml.YAMLError as e:
        raise RuntimeError(f"Error parsing config file {file_name}: {e}")
```

**Called by**: `main.py`
**Calls**: `__get_filename(env)`
**Purpose**: Loads and parses YAML configuration files with comprehensive error handling.

#### __get_filename(env: str) Function
```python
def __get_filename(env: str) -> str:
    if env == "dev":
        return "config/config_dev.yaml"
    else:
        return "config/config.yaml"
```
**Called by**: `load_config()`
**Purpose**: Returns appropriate config file path based on environment.

## Logging System

### slack.py - Slack Integration

#### Slack Class
```python
class Slack:
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url

    def send_message(self, message: str, username: str = "Music Charts Pipeline") -> bool:
        try:
            payload = {
                "text": message,
                "username": username
            }
            response = requests.post(
                self.webhook_url,
                data=json.dumps(payload),
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
```
**Called by**: `SlackLogHandler`
**Purpose**: Sends formatted messages to Slack webhook.

### slack_log_handler.py - Custom Log Handler

#### SlackLogHandler Class
```python
class SlackLogHandler(logging.Handler):
    def __init__(self, slack_client: Slack):
        super().__init__()
        self.slack_client = slack_client

    def emit(self, record):
        try:
            message = self.format(record)
            if record.levelno >= logging.ERROR:
                emoji = "🚨"
            elif record.levelno >= logging.WARNING:
                emoji = "⚠️"
            else:
                emoji = "ℹ️"
            slack_message = f"{emoji} *Music Charts Pipeline*\n```{message}```"
            self.slack_client.send_message(slack_message)
        except Exception:
            pass
```
**Called by**: Python logging system
**Calls**: `slack_client.send_message()`
**Purpose**: Formats log messages and sends them to Slack with appropriate emojis.

## Web Scraping Client

### music_charts_client.py - Web Scraping Engine

#### MusicChartsClient Class Initialization
```python
class MusicChartsClient:
    def __init__(self, base_url: str, retry_limit: int = 3, retry_delay: int = 2, logger: Logger = None):
        self.base_url = base_url
        self.retry_limit = retry_limit
        self.retry_delay = retry_delay
        self.logger = logger

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
```
**Called by**: `main.py`
**Purpose**: Initializes HTTP session with browser-like headers for web scraping.

#### _make_request(url: str) Method
```python
def _make_request(self, url: str) -> Optional[str]:
    for attempt in range(self.retry_limit):
        try:
            self._log("info", f"Requesting: {url} (attempt {attempt + 1})")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            self._log("warning", f"Request failed (attempt {attempt + 1}): {e}")
            if attempt < self.retry_limit - 1:
                time.sleep(self.retry_delay)
            else:
                self._log("error", f"Failed to fetch {url} after {self.retry_limit} attempts")
                return None
    return None
```
**Called by**: `scrape_chart_data()`
**Calls**: `self._log()`, `self.session.get()`
**Purpose**: Makes HTTP requests with retry logic and exponential backoff.

#### get_chart_dates(chart_type: str) Method
```python
def get_chart_dates(self, chart_type: str = "singles") -> List[str]:
    self._log("info", f"Generating {chart_type} chart dates")
    dates = []
    start_date = datetime(2020, 1, 4)
    end_date = datetime.now()
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=7)
    self._log("info", f"Generated {len(dates)} potential {chart_type} chart dates")
    return dates
```
**Called by**: `raw_musiccharts_job()` in ETL pipeline
**Purpose**: Generates weekly chart dates from 2020 to present for scraping.

#### scrape_chart_data(chart_type: str, chart_date: str) Method
```python
def scrape_chart_data(self, chart_type: str, chart_date: str) -> List[Dict]:
    date_obj = datetime.strptime(chart_date, "%Y-%m-%d")
    url_date = date_obj.strftime("%Y-%m-%d")
    if chart_type == "singles":
        chart_url = f"{self.base_url}/singles-chart/{url_date}"
    elif chart_type == "albums":
        chart_url = f"{self.base_url}/album-chart/{url_date}"
    else:
        self._log("error", f"Unknown chart type: {chart_type}")
        return []
    html_content = self._make_request(chart_url)
    if not html_content:
        return []
    return self._extract_chart_entries(html_content, chart_type, chart_date)
```
**Called by**: `raw_musiccharts_job()` in ETL pipeline
**Calls**: `self._make_request()`, `self._extract_chart_entries()`
**Purpose**: Constructs chart URLs and scrapes data for specific dates.

#### _extract_chart_entries(html_content: str, chart_type: str, chart_date: str) Method
```python
def _extract_chart_entries(self, html_content: str, chart_type: str, chart_date: str) -> List[Dict]:
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        if chart_type == "singles":
            return self._extract_singles_entries(soup, chart_date)
        elif chart_type == "albums":
            return self._extract_albums_entries(soup, chart_date)
        else:
            self._log("error", f"Unknown chart type: {chart_type}")
            return []
    except Exception as e:
        self._log("error", f"Error parsing HTML for {chart_date}: {e}")
        return []
```
**Called by**: `scrape_chart_data()`
**Calls**: `self._extract_singles_entries()` or `self._extract_albums_entries()`
**Purpose**: Parses HTML and delegates to specific extraction methods.

## ETL Pipeline

### musiccharts_etl.py - Main ETL Logic

#### raw_musiccharts_job() Function - The Core Pipeline
```python
def raw_musiccharts_job(
        music_charts_client: MusicChartsClient,
        config: dict,
        logger: Logger
    ) -> None:
```
**Called by**: `main()` in `main.py`
**Purpose**: Orchestrates the complete ETL process.

##### Step 1: Configuration Extraction
```python
logger.info("Starting Music Charts ETL job...")
chart_types = config["chart_types"]
request_delay = config.get("request_delay", 2)
date_range = config.get("date_range", {})
total_processed = 0
```
**Purpose**: Extracts configuration parameters for the ETL job.

##### Step 2: Database Connection
```python
database_config = config.get("database_tables", {})
database = SupabaseDatabase(
    tables=database_config,
    user=os.getenv("SUPABASE_USER", "postgres"),
    password=os.getenv("SUPABASE_PASSWORD", ""),
    host=os.getenv("SUPABASE_HOST", "localhost"),
    port=os.getenv("SUPABASE_PORT", "5432"),
    dbname=os.getenv("SUPABASE_DB", "postgres"),
    read_batch_size=config.get("constants", {}).get("read_batch_size", 5000),
    write_batch_size=config.get("constants", {}).get("write_batch_size", 100),
    logger=logger
)
```
**Classes Instantiated**: `SupabaseDatabase`
**Purpose**: Establishes direct database connection for storing chart data as JSON.

##### Step 3: Chart Type Processing Loop
```python
for chart_type in chart_types:
    logger.info(f"Processing {chart_type} charts...")
    try:
        # Get available dates from client
        available_dates = music_charts_client.get_chart_dates(chart_type)
        logger.info(f"Found {len(available_dates)} available {chart_type} chart dates")

        # Get existing dates from storage
        existing_dates = storage.get_existing_chart_dates(chart_type)
        logger.info(f"Found {len(existing_dates)} existing {chart_type} chart dates in database")

        # Filter for new dates only
        new_dates = filter_new_dates(available_dates, existing_dates, date_range)
        logger.info(f"Processing {len(new_dates)} new {chart_type} chart dates")
```
**Function Calls**:
- `music_charts_client.get_chart_dates(chart_type)`
- `database.get_existing_chart_dates(chart_type)`
- `filter_new_dates()` from `raw/common.py`

**Purpose**: Determines which chart dates need to be processed by comparing available vs existing data.

##### Step 4: Individual Chart Processing Loop
```python
for i, chart_date in enumerate(new_dates, 1):
    logger.info(f"[{i}/{len(new_dates)}] Processing {chart_type} chart for {chart_date}")
    try:
        # Extract data
        chart_data = music_charts_client.scrape_chart_data(chart_type, chart_date)
        if chart_data:
            # Validate data
            validated_data = validate_chart_data(chart_data, chart_type)
            if validated_data:
                # Load data as JSON
                database.save_chart_data(chart_type, chart_date, validated_data)
                logger.info(f"Saved {len(validated_data)} {chart_type} entries for {chart_date}")
                total_processed += len(validated_data)
            else:
                logger.warning(f"No valid data for {chart_type} chart on {chart_date}")
        else:
            logger.warning(f"No data found for {chart_type} chart on {chart_date}")
    except Exception as e:
        logger.error(f"Error processing {chart_type} chart for {chart_date}: {e}")
        continue

    # Rate limiting
    if i < len(new_dates):
        time.sleep(request_delay)
```
**Function Calls**:
- `music_charts_client.scrape_chart_data(chart_type, chart_date)`
- `validate_chart_data()` from `raw/common.py`
- `storage.save_singles_charts()` or `storage.save_albums_charts()`

**Purpose**: Processes each chart date individually with error handling and rate limiting.

##### Step 5: Final Statistics
```python
stats = storage.get_table_stats()
logger.info(f"Music Charts ETL job completed successfully. Total entries processed: {total_processed}")
logger.info(f"Table stats: {stats}")
```
**Function Calls**: `storage.get_table_stats()`
**Purpose**: Reports final processing statistics.

## Database System

The pipeline uses a direct database connection to store chart data as JSON.

### SupabaseDatabase Class - PostgreSQL Database

#### Database Schema
The new schema stores entire chart data as JSON in a single table:
```sql
CREATE TABLE music_archive (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chart_date DATE NOT NULL,
    chart_type TEXT NOT NULL,  -- 'single_chart' or 'album_chart'
    chart_data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT (now() AT TIME ZONE 'utc'::text)
);
CREATE UNIQUE INDEX idx_music_archive_unique
ON music_archive(chart_date, chart_type);
```

#### Initialization and Connection
```python
class SupabaseDatabase:
    def __init__(self, tables: dict, user: str, password: str, host: str, port: str, dbname: str,
                 read_batch_size: int, write_batch_size: int, logger: Logger):
        self.supabase_raw = SupabaseRaw(tables["raw"], read_batch_size, write_batch_size, logger)
        self.supabase_log = SupabaseLog(tables["log"]["log_table"], logger)
        # Connection parameters...
```
**Called by**: `raw_musiccharts_job()`
**Purpose**: Establishes direct PostgreSQL connection to Supabase database.

#### Data Insertion with JSON Storage
```python
def save_chart_data(self, conn, cursor, chart_type: str, chart_date: str, data: List[Dict]) -> None:
    table_name = self.tables["charts"]

    # Create table if it doesn't exist
    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            chart_date DATE NOT NULL,
            chart_type TEXT NOT NULL,
            chart_data JSONB NOT NULL,
            created_at TIMESTAMPTZ DEFAULT (now() AT TIME ZONE 'utc'::text)
        );
        CREATE UNIQUE INDEX IF NOT EXISTS idx_{table_name}_unique
        ON {table_name}(chart_date, chart_type);
    """
    cursor.execute(create_table_query)

    # Insert the chart data as a single JSON record
    insert_query = f"""
        INSERT INTO {table_name} (chart_date, chart_type, chart_data)
        VALUES (%s, %s, %s)
        ON CONFLICT (chart_date, chart_type)
        DO UPDATE SET
            chart_data = EXCLUDED.chart_data,
            created_at = (now() AT TIME ZONE 'utc'::text)
    """

    chart_data_json = json.dumps(data)
    cursor.execute(insert_query, (chart_date, chart_type, chart_data_json))
```
**Called by**: `raw_musiccharts_job()` for each chart date
**Purpose**: Stores entire chart data as JSON with upsert logic to handle updates.

#### Data Retrieval
```python
def get_existing_chart_dates(self, cursor, chart_type: str) -> set:
    table_name = self.tables["charts"]
    try:
        query = f"SELECT DISTINCT chart_date FROM {table_name} WHERE chart_type = %s"
        cursor.execute(query, (chart_type,))
        results = cursor.fetchall()
        return {str(row[0]) for row in results}
    except Exception as e:
        self.logger.error(f"Error fetching existing chart dates for {chart_type}: {e}")
        return set()
```
**Called by**: `raw_musiccharts_job()` to determine which dates to process
**Purpose**: Retrieves existing chart dates to avoid reprocessing.

## Data Validation

### common.py - Data Processing Utilities

#### validate_chart_data() Function
```python
def validate_chart_data(data: List[Dict], chart_type: str) -> List[Dict]:
    validated_data = []
    title_field = f"{chart_type[:-1]}_title"  # "singles" -> "single_title"

    for entry in data:
        try:
            title = entry.get(title_field, "").strip()
            artist = entry.get("artist", "").strip()
            chart_date = entry.get("chart_date", "").strip()
            position = entry.get("position")

            # Validate required fields
            if not title or not artist or not chart_date or position is None:
                continue

            # Validate position is positive integer
            try:
                position = int(position)
                if position <= 0:
                    continue
            except (ValueError, TypeError):
                continue

            # Validate date format
            try:
                datetime.strptime(chart_date, "%Y-%m-%d")
            except ValueError:
                continue

            # Create cleaned entry
            cleaned_entry = {
                title_field: title,
                "artist": artist,
                "chart_date": chart_date,
                "position": position,
                "url": entry.get("url", "").strip()
            }
            validated_data.append(cleaned_entry)
        except Exception:
            continue

    return validated_data
```
**Called by**: `raw_musiccharts_job()` after data extraction
**Purpose**: Validates and cleans scraped data, ensuring data quality and consistency.

#### filter_new_dates() Function
```python
def filter_new_dates(available_dates: List[str], existing_dates: Set[str], date_range: Dict = None) -> List[str]:
    new_dates = []
    for date_str in available_dates:
        # Skip if already processed
        if date_str in existing_dates:
            continue

        # Apply date range filter if specified
        if date_range:
            try:
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                start_year = date_range.get("start_year")
                end_year = date_range.get("end_year")

                if start_year and date_obj.year < start_year:
                    continue
                if end_year and date_obj.year > end_year:
                    continue
            except ValueError:
                continue

        new_dates.append(date_str)

    new_dates.sort()
    return new_dates
```
**Called by**: `raw_musiccharts_job()` to filter dates for processing
**Purpose**: Filters available dates against existing data and configuration constraints.

## Complete Function Call Flow

Here's the complete execution flow from start to finish:

### 1. Application Startup
```
main.py:__main__
├── Environment detection (is_dev)
├── Environment variables loading
├── load_config(env) → config_loader.py
├── Logger setup
├── Slack integration (production only)
│   ├── Slack.__init__()
│   └── SlackLogHandler.__init__()
├── MusicChartsClient.__init__()
└── main()
```

### 2. Main ETL Execution
```
main.py:main()
└── raw_musiccharts_job(client, config, logger) → musiccharts_etl.py
    ├── Database connection
    │   └── SupabaseDatabase.__init__()
    │       ├── SupabaseRaw.__init__()
    │       └── SupabaseLog.__init__()
    │
    └── For each chart_type in config["chart_types"]:
        ├── music_charts_client.get_chart_dates(chart_type)
        ├── database.get_existing_chart_dates(chart_type)
        ├── filter_new_dates(available, existing, date_range) → common.py
        │
        └── For each chart_date in new_dates:
            ├── music_charts_client.scrape_chart_data(chart_type, chart_date)
            │   ├── _make_request(chart_url)
            │   │   └── session.get() [with retry logic]
            │   └── _extract_chart_entries(html, chart_type, chart_date)
            │       └── _extract_singles_entries() OR _extract_albums_entries()
            │
            ├── validate_chart_data(chart_data, chart_type) → common.py
            │
            ├── database.save_chart_data(chart_type, chart_date, validated_data)
            │   └── [JSON storage with upsert logic]
            │
            └── time.sleep(request_delay) [Rate limiting]
```

### 3. Error Handling Flow
```
Exception occurs at any level
├── Local try/catch blocks log errors
├── Continue processing (graceful degradation)
├── SlackLogHandler.emit() [if ERROR level in production]
│   └── slack.send_message()
└── Final statistics reporting
```

### 4. Data Flow Summary

1. **Configuration** → Loaded from YAML files based on environment
2. **Available Dates** → Generated by client (weekly dates from 2020)
3. **Existing Dates** → Retrieved from database to avoid duplicates
4. **New Dates** → Filtered list of dates to process
5. **Raw Data** → Scraped HTML parsed into structured data
6. **Validated Data** → Cleaned and validated entries
7. **Stored Data** → Persisted as JSON in PostgreSQL database

### 5. Key Design Patterns

#### JSON Storage Pattern
```python
chart_data_json = json.dumps(data)
cursor.execute(insert_query, (chart_date, chart_type, chart_data_json))
```
Used for: Storing entire chart data as JSON instead of individual rows

#### Retry Pattern
```python
for attempt in range(retry_limit):
    try:
        result = operation()
        return result
    except Exception:
        if attempt < retry_limit - 1:
            time.sleep(retry_delay)
        else:
            return None
```
Used for: HTTP requests, database operations

#### Batch Processing Pattern
```python
for i in range(0, len(data), batch_size):
    batch = data[i:i + batch_size]
    process_batch(batch)
```
Used for: Database insertions, API calls

#### Strategy Pattern
```python
if chart_type == "singles":
    return self._extract_singles_entries(soup, chart_date)
elif chart_type == "albums":
    return self._extract_albums_entries(soup, chart_date)
```
Used for: Different chart type processing

### 6. Configuration-Driven Behavior

The pipeline's behavior is controlled by configuration files:

- **chart_types**: Determines which charts to process
- **date_range**: Filters which years to process
- **request_delay**: Controls rate limiting
- **retry_limit**: Controls resilience
- **batch_sizes**: Controls performance vs memory usage

### 7. Logging and Monitoring

Every major operation is logged with appropriate levels:
- **INFO**: Normal operation progress
- **WARNING**: Non-fatal issues (fallbacks, missing data)
- **ERROR**: Fatal issues (sent to Slack in production)

This architecture ensures the pipeline is:
- **Resilient**: Multiple fallback mechanisms
- **Configurable**: Environment-specific behavior
- **Monitorable**: Comprehensive logging
- **Maintainable**: Clear separation of concerns
- **Scalable**: Batch processing and rate limiting

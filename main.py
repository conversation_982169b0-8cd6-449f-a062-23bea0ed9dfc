import os
import sys
from logging import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>F<PERSON>, ERROR

from config.config_loader import load_config
from libs.database.supabase import SupabaseDatabase
from libs.clients.music_charts_client import MusicChartsClient
from libs.logging.slack_log_handler import <PERSON>lack<PERSON>ogHandler
from libs.logging.slack import Slack
from raw.musiccharts_etl import raw_musiccharts_job


is_dev = len(sys.argv) > 1 and sys.argv[1] == '-dev'
if is_dev:
    from dotenv import load_dotenv
    load_dotenv()

SUPABASE_USER = os.getenv("SUPABASE_USER", "")
SUPABASE_PASSWORD = os.getenv("SUPABASE_PASSWORD", "")
SUPABASE_HOST = os.getenv("SUPABASE_HOST", "")
SUPABASE_PORT = os.getenv("SUPABASE_PORT", "")
SUPABASE_DBNAME = os.getenv("SUPABASE_DBNAME", "")

try:
    if is_dev:
        config = load_config("dev")
    else:
        config = load_config("prod")
except Exception as e:
    sys.exit(1)

if not config:
    sys.exit(1)

try:
    tables = config["database_tables"]
    read_batch_size = config["constants"]["read_batch_size"]
    write_batch_size = config["constants"]["write_batch_size"]
    retry_limit = config["constants"]["retry_limit"]
    retry_delay = config["constants"]["retry_delay"]
    chart_start_date = config["constants"]["chart_start_date"]
    max_chart_entries = config["constants"]["max_chart_entries"]
    default_timeout = config["constants"]["default_timeout"]
    musiccharts_config = config["jobs"]["raw_music_charts"]
except KeyError as e:
    sys.exit(1)

if is_dev:
    logger = Logger("Console-logger")
    logger.setLevel(INFO)
    console_handler = StreamHandler()
    console_handler.setLevel(INFO)
    formatter = Formatter("[%(asctime)s] [%(levelname)s] [%(module)s.%(funcName)s Line %(lineno)d] - %(message)s")
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
else:
    logger = Logger("Slack-logger")
    logger.setLevel(INFO)
    SLACK_WEBHOOK = os.getenv("SLACK_WEBHOOK", "")
    slack = Slack(SLACK_WEBHOOK)
    logger.addHandler(SlackLogHandler(slack, ERROR))

music_charts_client = MusicChartsClient(
    base_url=musiccharts_config["base_url"],
    retry_limit=retry_limit,
    retry_delay=retry_delay,
    chart_start_date=chart_start_date,
    max_chart_entries=max_chart_entries,
    default_timeout=default_timeout,
    logger=logger
)

database = SupabaseDatabase(tables, SUPABASE_USER, SUPABASE_PASSWORD,
                           SUPABASE_HOST, SUPABASE_PORT, SUPABASE_DBNAME,
                           read_batch_size, write_batch_size, logger)

def main():
    while True:
        logger.info("Pipeline started.")
        
        raw_musiccharts_job(music_charts_client, database, musiccharts_config, logger)
        
        logger.info("Pipeline executed.")
        
        if is_dev:
            break

if __name__ == "__main__":
    main()

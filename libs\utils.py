import time
from datetime import datetime, date
from typing import List, Dict, Set, Optional
import psycopg


def convert_to_date(datetime_str: str) -> date:
    if datetime_str is None:
        return None
    
    dt_object = datetime.strptime(datetime_str, "%Y-%m-%dT%H:%M:%S")
    return dt_object.date()


def get_bounding_datetime(input_date: date) -> tuple[str, str]:
    start_of_day = datetime.combine(input_date, datetime.min.time())
    end_of_day = datetime.combine(input_date, datetime.max.time().replace(microsecond=0))
    
    return start_of_day.strftime("%Y-%m-%d %H:%M:%S"), end_of_day.strftime("%Y-%m-%d %H:%M:%S")


def retry_on_db_error(retries: int = 3, delay: int = 120):
    def decorator(func):
        def wrapper(*args, **kwargs):
            attempt = 0
            while attempt < retries:
                try:
                    return func(*args, **kwargs)
                except (psycopg.DatabaseError, psycopg.OperationalError, psycopg.InterfaceError):
                    attempt += 1
                    if attempt < retries:
                        time.sleep(delay)
                    else:
                        raise
        return wrapper
    return decorator


def is_valid_date(date_str: str) -> bool:
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def is_date_in_range(date_str: str, date_range: Dict) -> bool:
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")

        start_year = date_range.get("start_year")
        if start_year and date_obj.year < start_year:
            return False

        end_year = date_range.get("end_year")
        if end_year and date_obj.year > end_year:
            return False

        return True

    except ValueError:
        return False


def filter_new_dates(available_dates: List[str], existing_dates: Set[str],
                    date_range: Optional[Dict] = None) -> List[str]:
    new_dates = []

    for date_str in available_dates:
        if date_str in existing_dates:
            continue

        if date_range and not is_date_in_range(date_str, date_range):
            continue

        new_dates.append(date_str)

    new_dates.sort()
    return new_dates


def remove_duplicates_by_key(data: List[Dict], key_fields: List[str]) -> List[Dict]:
    seen_keys = set()
    unique_data = []

    for entry in data:
        key_values = tuple(entry.get(field, "") for field in key_fields)

        if key_values not in seen_keys:
            seen_keys.add(key_values)
            unique_data.append(entry)

    return unique_data

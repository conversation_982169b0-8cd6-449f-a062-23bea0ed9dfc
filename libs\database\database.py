from datetime import datetime
from typing import Optional, List, Dict
from abc import ABC, abstractmethod

class Database(ABC):
    @abstractmethod
    def save_job_start_time(self, job_name: str) -> Optional[datetime]:
        pass

    @abstractmethod
    def save_job_completed_time(self, status: str) -> None:
        pass

    @abstractmethod
    def update_last_processed_timestamp(self) -> None:
        pass

    @abstractmethod
    def save_chart_data(self, chart_type: str, chart_date: str, data: List[Dict]) -> None:
        pass

    @abstractmethod
    def get_existing_chart_dates(self, chart_type: str) -> set:
        pass

from datetime import datetime
from typing import Optional
from logging import Logger

class SupabaseLog:
    def __init__(self, log_table: str, logger: Logger):
        self.log_table = log_table
        self.logger = logger
        self.current_job_id = None

    def save_job_start_time(self, conn, cursor, job_name: str) -> Optional[datetime]:
        try:
            insert_query = f"""
                INSERT INTO {self.log_table} (job_name, start_time, status)
                VALUES (%s, %s, %s)
                RETURNING id
            """
            start_time = datetime.utcnow()
            cursor.execute(insert_query, (job_name, start_time, "Running"))
            result = cursor.fetchone()
            if result:
                self.current_job_id = result[0]
                self.logger.info(f"Job {job_name} started with ID {self.current_job_id}")
            last_processed_query = f"""
                SELECT MAX(completed_time)
                FROM {self.log_table}
                WHERE job_name = %s AND status = 'Success'
            """
            cursor.execute(last_processed_query, (job_name,))
            result = cursor.fetchone()
            last_processed_date = result[0] if result and result[0] else None
            if last_processed_date:
                self.logger.info(f"Last processed date: {last_processed_date}")
            else:
                self.logger.info("No previous successful runs found")
            return last_processed_date
        except Exception as e:
            self.logger.error(f"Error saving job start time: {e}")
            raise

    def save_job_completed_time(self, conn, cursor, status: str) -> None:
        if not self.current_job_id:
            self.logger.warning("No current job ID to update")
            return
        try:
            completed_time = datetime.utcnow()
            update_query = f"""
                UPDATE {self.log_table}
                SET completed_time = %s, status = %s
                WHERE id = %s
            """
            cursor.execute(update_query, (completed_time, status, self.current_job_id))
            self.logger.info(f"Job {self.current_job_id} completed with status: {status}")
        except Exception as e:
            self.logger.error(f"Error saving job completed time: {e}")
            raise

    def update_last_processed_timestamp(self, conn, cursor) -> None:
        pass

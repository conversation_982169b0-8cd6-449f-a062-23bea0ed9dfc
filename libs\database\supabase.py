from datetime import datetime
from typing import Optional, List, Dict
from logging import Logger
import psycopg

from libs.database.database import Database
from libs.database.supabase_raw import SupabaseRaw
from libs.database.supabase_log import SupabaseLog
from libs.utils import retry_on_db_error

class SupabaseDatabase(Database):
    def __init__(self, tables: dict, user: str, password: str, host: str, port: str, dbname: str,
                 read_batch_size: int, write_batch_size: int, logger: Logger):
        self.supabase_raw = SupabaseRaw(tables["raw"], read_batch_size, write_batch_size, logger)
        self.supabase_log = SupabaseLog(tables["log"]["log_table"], logger)
        self.user = user
        self.password = password
        self.host = host
        self.port = port
        self.dbname = dbname
        self.logger = logger

    @retry_on_db_error()
    def save_job_start_time(self, job_name: str) -> Optional[datetime]:
        try:
            with psycopg.connect(
                user=self.user, password=self.password, host=self.host,
                port=self.port, dbname=self.dbname, autocommit=True
            ) as conn:
                with conn.cursor() as cursor:
                    return self.supabase_log.save_job_start_time(conn, cursor, job_name)
        except Exception as e:
            self.logger.error(f"Database connection error in save_job_start_time: {e}")
            raise

    @retry_on_db_error()
    def save_job_completed_time(self, status: str) -> None:
        try:
            with psycopg.connect(
                user=self.user, password=self.password, host=self.host,
                port=self.port, dbname=self.dbname, autocommit=True
            ) as conn:
                with conn.cursor() as cursor:
                    self.supabase_log.save_job_completed_time(conn, cursor, status)
        except Exception as e:
            self.logger.error(f"Database connection error in save_job_completed_time: {e}")
            raise

    @retry_on_db_error()
    def update_last_processed_timestamp(self) -> None:
        try:
            with psycopg.connect(
                user=self.user, password=self.password, host=self.host,
                port=self.port, dbname=self.dbname, autocommit=True
            ) as conn:
                with conn.cursor() as cursor:
                    self.supabase_log.update_last_processed_timestamp(conn, cursor)
        except Exception as e:
            self.logger.error(f"Database connection error in update_last_processed_timestamp: {e}")
            raise

    @retry_on_db_error()
    def save_chart_data(self, chart_type: str, chart_date: str, data: List[Dict]) -> None:
        try:
            with psycopg.connect(
                user=self.user, password=self.password, host=self.host,
                port=self.port, dbname=self.dbname, autocommit=True
            ) as conn:
                with conn.cursor() as cursor:
                    self.supabase_raw.save_chart_data(conn, cursor, chart_type, chart_date, data)
        except Exception as e:
            self.logger.error(f"Database connection error in save_chart_data: {e}")
            raise

    @retry_on_db_error()
    def get_existing_chart_dates(self, chart_type: str) -> set:
        try:
            with psycopg.connect(
                user=self.user, password=self.password, host=self.host,
                port=self.port, dbname=self.dbname, autocommit=True
            ) as conn:
                with conn.cursor() as cursor:
                    return self.supabase_raw.get_existing_chart_dates(cursor, chart_type)
        except Exception as e:
            self.logger.error(f"Database connection error in get_existing_chart_dates: {e}")
            return set()

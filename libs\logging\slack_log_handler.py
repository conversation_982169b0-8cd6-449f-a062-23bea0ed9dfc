import logging
from libs.logging.slack import Slack

class Slack<PERSON>og<PERSON><PERSON><PERSON>(logging.Handler):
    def __init__(self, slack_client: Slack):
        super().__init__()
        self.slack_client = slack_client

    def emit(self, record):
        try:
            message = self.format(record)
            if record.levelno >= logging.ERROR:
                emoji = "🚨"
            elif record.levelno >= logging.WARNING:
                emoji = "⚠️"
            else:
                emoji = "ℹ️"
            slack_message = f"{emoji} *Music Charts Pipeline*\n```{message}```"
            self.slack_client.send_message(slack_message)
        except Exception:
            pass

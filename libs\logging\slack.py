import requests
import json

class Slack:
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url

    def send_message(self, message: str, username: str = "Music Charts Pipeline") -> bool:
        try:
            payload = {
                "text": message,
                "username": username
            }
            response = requests.post(
                self.webhook_url,
                data=json.dumps(payload),
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False

import yaml

def load_config(env: str = "prod") -> dict | None:
    file_name = __get_filename(env)
    try:
        with open(file_name, "r", encoding="utf-8") as stream:
            return yaml.safe_load(stream)
    except FileNotFoundError:
        raise RuntimeError(f"Config file {file_name} not found")
    except PermissionError:
        raise RuntimeError(f"Permission denied to read config file {file_name}")
    except OSError as e:
        raise RuntimeError(f"Error reading config file {file_name}: {e}")
    except yaml.YAMLError as e:
        raise RuntimeError(f"Error parsing config file {file_name}: {e}")

def __get_filename(env: str) -> str:
    if env == "dev":
        return "config/config_dev.yaml"
    else:
        return "config/config.yaml"
